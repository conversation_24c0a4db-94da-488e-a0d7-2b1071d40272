import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/gobang_controller.dart';

/// 五子棋棋盘组件
class GobangBoard extends StatefulWidget {
  const GobangBoard({super.key});

  @override
  State<GobangBoard> createState() => _GobangBoardState();
}

class _GobangBoardState extends State<GobangBoard> {
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<GobangController>();

    return Container(
      decoration: BoxDecoration(
        // 木质纹理背景
        color: const Color(0xFFD2B48C), // 浅棕色木质感
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.3), blurRadius: 8.r, offset: Offset(2.w, 2.h))],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: GestureDetector(
          onTapDown: (details) => _onTapDown(details, controller),
          behavior: HitTestBehavior.opaque, // 确保整个区域都能接收点击事件
          child: Obx(
            () => CustomPaint(
              painter: BoardPainter(boardSize: controller.boardSize, showCoordinates: controller.showCoordinates.value),
              child: SizedBox(width: double.infinity, height: double.infinity, child: _buildPiecesWidget(controller)),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理点击事件
  void _onTapDown(TapDownDetails details, GobangController controller) async {
    print('_onTapDown called!');

    if (controller.isGameOver.value) {
      print('Game is over, ignoring click');
      return;
    }

    final localPosition = details.localPosition;
    print('Click position: $localPosition');

    // 获取当前widget的渲染盒子来计算实际大小
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      print('RenderBox is null');
      return;
    }

    final size = renderBox.size;
    final boardSize = controller.boardSize;

    // 确保棋盘是正方形，取宽度和高度的最小值
    final boardDimension = size.width < size.height ? size.width : size.height;
    final cellSize = boardDimension / (boardSize - 1);

    // 转换为网格坐标
    final row = (localPosition.dy / cellSize).round();
    final col = (localPosition.dx / cellSize).round();
    print('Grid coordinates: row=$row, col=$col');

    // 检查坐标是否有效
    if (row >= 0 && row < boardSize && col >= 0 && col < boardSize) {
      print('Valid coordinates, calling makeMove');
      await controller.makeMove(row, col);
    } else {
      print('Invalid coordinates: row=$row, col=$col, boardSize=$boardSize');
    }
  }

  /// 构建棋子Widget
  Widget _buildPiecesWidget(GobangController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Obx(() {
          final pieces = <Widget>[];
          final boardSize = controller.boardSize;
          final size = constraints.biggest;

          // 确保棋盘是正方形，取宽度和高度的最小值
          final boardDimension = size.width < size.height ? size.width : size.height;
          final cellSize = boardDimension / (boardSize - 1);
          final pieceSize = cellSize * 0.8; // 棋子大小为格子的80%

          for (int row = 0; row < boardSize; row++) {
            for (int col = 0; col < boardSize; col++) {
              final pieceType = controller.board[row][col];
              if (pieceType != 0) {
                print(
                  'Adding piece at ($row, $col) with type $pieceType at position (${col * cellSize}, ${row * cellSize})',
                );
                pieces.add(
                  Positioned(
                    left: col * cellSize - pieceSize / 2,
                    top: row * cellSize - pieceSize / 2,
                    child: AnimatedContainer(
                      duration: controller.enableAnimation.value ? const Duration(milliseconds: 300) : Duration.zero,
                      curve: Curves.elasticOut,
                      child: GobangPiece(type: pieceType, size: pieceSize),
                    ),
                  ),
                );
              }
            }
          }

          print('Total pieces: ${pieces.length}');
          return Stack(children: pieces);
        });
      },
    );
  }
}

/// 棋盘绘制器
class BoardPainter extends CustomPainter {
  final int boardSize;
  final bool showCoordinates;

  BoardPainter({required this.boardSize, required this.showCoordinates});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color =
          const Color(0xFF8B4513) // 深棕色线条
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // 确保棋盘是正方形，取宽度和高度的最小值
    final boardDimension = size.width < size.height ? size.width : size.height;
    final cellSize = boardDimension / (boardSize - 1);

    // 绘制网格线
    for (int i = 0; i < boardSize; i++) {
      // 水平线
      canvas.drawLine(Offset(0, i * cellSize), Offset(boardDimension, i * cellSize), paint);

      // 垂直线
      canvas.drawLine(Offset(i * cellSize, 0), Offset(i * cellSize, boardDimension), paint);
    }

    // 绘制天元和星位
    _drawStarPoints(canvas, Size(boardDimension, boardDimension), cellSize);

    // 绘制坐标
    if (showCoordinates) {
      _drawCoordinates(canvas, Size(boardDimension, boardDimension), cellSize);
    }
  }

  /// 绘制星位点
  void _drawStarPoints(Canvas canvas, Size size, double cellSize) {
    final paint = Paint()
      ..color = const Color(0xFF8B4513)
      ..style = PaintingStyle.fill;

    final starPoints = <Offset>[];

    if (boardSize == 15) {
      // 标准15x15棋盘的星位
      starPoints.addAll([
        Offset(3 * cellSize, 3 * cellSize),
        Offset(11 * cellSize, 3 * cellSize),
        Offset(7 * cellSize, 7 * cellSize), // 天元
        Offset(3 * cellSize, 11 * cellSize),
        Offset(11 * cellSize, 11 * cellSize),
      ]);
    }

    for (final point in starPoints) {
      canvas.drawCircle(point, 3.r, paint);
    }
  }

  /// 绘制坐标
  void _drawCoordinates(Canvas canvas, Size size, double cellSize) {
    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    for (int i = 0; i < boardSize; i++) {
      // 数字坐标（行）
      textPainter.text = TextSpan(
        text: '${i + 1}',
        style: TextStyle(color: const Color(0xFF8B4513), fontSize: 10.sp, fontWeight: FontWeight.bold),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(-20.w, i * cellSize - textPainter.height / 2));

      // 字母坐标（列）
      textPainter.text = TextSpan(
        text: String.fromCharCode(65 + i), // A, B, C...
        style: TextStyle(color: const Color(0xFF8B4513), fontSize: 10.sp, fontWeight: FontWeight.bold),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(i * cellSize - textPainter.width / 2, -20.h));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}

/// 棋子组件
class GobangPiece extends StatelessWidget {
  final int type; // 1: 黑子, 2: 白子
  final double size;

  const GobangPiece({super.key, required this.type, required this.size});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: type == 1
            ? const RadialGradient(colors: [Color(0xFF404040), Color(0xFF000000)], stops: [0.3, 1.0])
            : const RadialGradient(colors: [Color(0xFFFFFFFF), Color(0xFFE0E0E0)], stops: [0.3, 1.0]),
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.5), blurRadius: 4.r, offset: Offset(1.w, 1.h))],
      ),
    );
  }
}
